import { <PERSON>, Get, Param, UseGuards, Logger, Inject } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ConversationManagerService } from './services/conversation-manager.service';
import { IConversationStateService } from './interfaces/conversation-state.interface';

/**
 * DEBUG CONTROLLER - Remove this in production
 * Used to inspect conversation state and debug repetition issues
 */
@Controller('debug/chat')
@UseGuards(JwtAuthGuard)
export class DebugConversationController {
  private readonly logger = new Logger(DebugConversationController.name);

  constructor(
    private readonly conversationManager: ConversationManagerService,
    @Inject('IConversationStateService')
    private readonly conversationState: IConversationStateService,
  ) {}

  /**
   * Get conversation context for debugging
   */
  @Get('session/:sessionId')
  async getConversationContext(@Param('sessionId') sessionId: string) {
    try {
      this.logger.log(`Debug: Getting conversation context for session ${sessionId}`);
      
      const context = await this.conversationState.getConversationContext(sessionId);
      
      if (!context) {
        return {
          error: 'Session not found',
          sessionId,
          exists: false
        };
      }

      return {
        sessionId,
        exists: true,
        messageCount: context.messages?.length || 0,
        messages: context.messages?.map((msg, index) => ({
          index,
          id: msg.id,
          role: msg.role,
          content: msg.content.substring(0, 100) + (msg.content.length > 100 ? '...' : ''),
          timestamp: msg.timestamp,
        })) || [],
        conversationStage: context.conversationStage,
        totalMessages: context.metadata?.totalMessages,
        lastActiveAt: context.metadata?.lastActiveAt,
        startedAt: context.metadata?.startedAt,
      };
    } catch (error) {
      this.logger.error(`Debug: Error getting conversation context for ${sessionId}`, error);
      return {
        error: error.message,
        sessionId,
        exists: false
      };
    }
  }

  /**
   * Get all active sessions (for debugging)
   */
  @Get('sessions')
  async getAllSessions() {
    try {
      // For now, just return a simple response since we can't access private cache
      return {
        message: 'Session listing not available - use specific session endpoint',
        totalSessions: 'unknown',
        sessions: []
      };
    } catch (error) {
      this.logger.error('Debug: Error getting all sessions', error);
      return {
        error: error.message,
        totalSessions: 0,
        sessions: []
      };
    }
  }

  /**
   * Clear a specific session (for testing)
   */
  @Get('clear/:sessionId')
  async clearSession(@Param('sessionId') sessionId: string) {
    try {
      this.logger.log(`Debug: Attempting to clear session ${sessionId}`);

      // Check if session exists first
      const existed = await this.conversationState.getConversationContext(sessionId);

      // For now, we can't directly clear from the interface
      // This would need to be implemented in the conversation state service
      return {
        sessionId,
        existed: !!existed,
        cleared: false,
        message: 'Session clearing not implemented - restart server to clear all sessions'
      };
    } catch (error) {
      this.logger.error(`Debug: Error clearing session ${sessionId}`, error);
      return {
        error: error.message,
        sessionId,
        cleared: false
      };
    }
  }
}
