import { <PERSON>, Get, Param, UseGuards, Logger } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ConversationManagerService } from './services/conversation-manager.service';
import { MemoryConversationStateService } from './services/memory-conversation-state.service';

/**
 * DEBUG CONTROLLER - Remove this in production
 * Used to inspect conversation state and debug repetition issues
 */
@Controller('debug/chat')
@UseGuards(JwtAuthGuard)
export class DebugConversationController {
  private readonly logger = new Logger(DebugConversationController.name);

  constructor(
    private readonly conversationManager: ConversationManagerService,
    private readonly conversationState: MemoryConversationStateService,
  ) {}

  /**
   * Get conversation context for debugging
   */
  @Get('session/:sessionId')
  async getConversationContext(@Param('sessionId') sessionId: string) {
    try {
      this.logger.log(`Debug: Getting conversation context for session ${sessionId}`);
      
      const context = await this.conversationState.getConversationContext(sessionId);
      
      if (!context) {
        return {
          error: 'Session not found',
          sessionId,
          exists: false
        };
      }

      return {
        sessionId,
        exists: true,
        messageCount: context.messages?.length || 0,
        messages: context.messages?.map((msg, index) => ({
          index,
          id: msg.id,
          role: msg.role,
          content: msg.content.substring(0, 100) + (msg.content.length > 100 ? '...' : ''),
          timestamp: msg.timestamp,
        })) || [],
        conversationStage: context.conversationStage,
        totalMessages: context.metadata?.totalMessages,
        lastActiveAt: context.metadata?.lastActiveAt,
        startedAt: context.metadata?.startedAt,
      };
    } catch (error) {
      this.logger.error(`Debug: Error getting conversation context for ${sessionId}`, error);
      return {
        error: error.message,
        sessionId,
        exists: false
      };
    }
  }

  /**
   * Get all active sessions (for debugging)
   */
  @Get('sessions')
  async getAllSessions() {
    try {
      // This is a hack to access the private cache - only for debugging
      const memoryService = this.conversationState as any;
      const cache = memoryService.cache;
      
      const sessions = [];
      for (const [sessionId, session] of cache.entries()) {
        sessions.push({
          sessionId,
          userId: session.userId,
          messageCount: session.context.messages?.length || 0,
          lastActiveAt: session.lastAccessedAt,
          conversationStage: session.context.conversationStage,
        });
      }

      return {
        totalSessions: sessions.length,
        sessions: sessions.slice(0, 10), // Limit to 10 for readability
      };
    } catch (error) {
      this.logger.error('Debug: Error getting all sessions', error);
      return {
        error: error.message,
        totalSessions: 0,
        sessions: []
      };
    }
  }

  /**
   * Clear a specific session (for testing)
   */
  @Get('clear/:sessionId')
  async clearSession(@Param('sessionId') sessionId: string) {
    try {
      this.logger.log(`Debug: Clearing session ${sessionId}`);
      
      // This is a hack to access the private cache - only for debugging
      const memoryService = this.conversationState as any;
      const cache = memoryService.cache;
      
      const existed = cache.has(sessionId);
      cache.delete(sessionId);
      
      return {
        sessionId,
        existed,
        cleared: true,
        message: existed ? 'Session cleared' : 'Session did not exist'
      };
    } catch (error) {
      this.logger.error(`Debug: Error clearing session ${sessionId}`, error);
      return {
        error: error.message,
        sessionId,
        cleared: false
      };
    }
  }
}
