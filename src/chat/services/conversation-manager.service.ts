import { Injectable, Logger, Inject } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import {
  ConversationContext,
  ChatMessage,
  UserIntent,
} from '../../common/llm/interfaces/llm.service.interface';
import { IConversationStateService } from '../interfaces/conversation-state.interface';

/**
 * Service responsible for managing conversation flow and state
 * Handles conversation lifecycle, message management, and context updates
 */
@Injectable()
export class ConversationManagerService {
  private readonly logger = new Logger(ConversationManagerService.name);

  constructor(
    @Inject('IConversationStateService')
    private readonly conversationStateService: IConversationStateService,
  ) {}

  /**
   * Initialize a new conversation session
   */
  async initializeConversation(
    userId: string,
    sessionId?: string,
  ): Promise<ConversationContext> {
    const finalSessionId = sessionId || this.generateSessionId();
    
    const context: ConversationContext = {
      sessionId: finalSessionId,
      userId,
      messages: [],
      discoveredEntities: [],
      userPreferences: {},
      conversationStage: 'greeting',
      metadata: {
        startedAt: new Date(),
        lastActiveAt: new Date(),
        totalMessages: 0,
        entitiesShown: [],
      },
    };

    await this.conversationStateService.setConversationContext(finalSessionId, context);
    
    this.logger.log(`Initialized new conversation session ${finalSessionId} for user ${userId}`);
    return context;
  }

  /**
   * Get existing conversation context or create new one
   */
  async getOrCreateConversation(
    userId: string,
    sessionId?: string,
  ): Promise<ConversationContext> {
    this.logger.log(`🔍 CONVERSATION DEBUG: getOrCreateConversation called with userId=${userId}, sessionId=${sessionId}`);

    if (sessionId) {
      this.logger.log(`🔍 CONVERSATION DEBUG: Looking for existing session ${sessionId}`);
      const existingContext = await this.conversationStateService.getConversationContext(sessionId);

      if (existingContext) {
        this.logger.log(`🔍 CONVERSATION DEBUG: Found existing context with ${existingContext.messages?.length || 0} messages`);

        // Verify the session belongs to the user
        if (existingContext.userId !== userId) {
          this.logger.warn(`Session ${sessionId} belongs to different user. Creating new session.`);
          this.logger.warn(`Expected userId: ${userId}, Found userId: ${existingContext.userId}`);
          return this.initializeConversation(userId);
        }

        this.logger.debug(`Retrieved existing conversation session ${sessionId}`);
        return existingContext;
      } else {
        this.logger.log(`🔍 CONVERSATION DEBUG: No existing context found for session ${sessionId} - creating new`);
      }
    } else {
      this.logger.log(`🔍 CONVERSATION DEBUG: No sessionId provided - creating new`);
    }

    // Create new conversation if session doesn't exist or no sessionId provided
    return this.initializeConversation(userId, sessionId);
  }

  /**
   * Add a message to the conversation and update context
   */
  async addMessage(
    sessionId: string,
    message: Omit<ChatMessage, 'id' | 'timestamp'>,
    intent?: UserIntent,
  ): Promise<ConversationContext> {
    const context = await this.conversationStateService.getConversationContext(sessionId);
    
    if (!context) {
      throw new Error(`Conversation session ${sessionId} not found`);
    }

    const chatMessage: ChatMessage = {
      id: uuidv4(),
      timestamp: new Date(),
      ...message,
    };

    // Add message to conversation
    context.messages.push(chatMessage);
    
    // Update metadata
    context.metadata.lastActiveAt = new Date();
    context.metadata.totalMessages = context.messages.length;

    // Update intent if provided
    if (intent) {
      context.currentIntent = intent;
    }

    // Update conversation stage based on message content and intent
    this.autoUpdateConversationStage(context, intent);

    // Save updated context
    await this.conversationStateService.setConversationContext(sessionId, context);

    this.logger.debug(
      `Added ${message.role} message to session ${sessionId}. Total messages: ${context.messages.length}`,
    );

    return context;
  }

  /**
   * Update user preferences in the conversation
   */
  async updateUserPreferences(
    sessionId: string,
    preferences: Partial<ConversationContext['userPreferences']>,
  ): Promise<ConversationContext> {
    const context = await this.conversationStateService.getConversationContext(sessionId);
    
    if (!context) {
      throw new Error(`Conversation session ${sessionId} not found`);
    }

    // Merge preferences
    context.userPreferences = {
      ...context.userPreferences,
      ...preferences,
    };

    context.metadata.lastActiveAt = new Date();

    await this.conversationStateService.setConversationContext(sessionId, context);

    this.logger.debug(`Updated user preferences for session ${sessionId}`);
    return context;
  }

  /**
   * Add discovered entities to the conversation
   */
  async addDiscoveredEntities(
    sessionId: string,
    entityIds: string[],
  ): Promise<ConversationContext> {
    const context = await this.conversationStateService.getConversationContext(sessionId);
    
    if (!context) {
      throw new Error(`Conversation session ${sessionId} not found`);
    }

    // Add new entities (avoid duplicates)
    const newEntities = entityIds.filter(id => !context.discoveredEntities.includes(id));
    context.discoveredEntities.push(...newEntities);

    // Update entities shown metadata
    context.metadata.entitiesShown.push(...newEntities);
    context.metadata.lastActiveAt = new Date();

    await this.conversationStateService.setConversationContext(sessionId, context);

    this.logger.debug(
      `Added ${newEntities.length} new entities to session ${sessionId}. Total: ${context.discoveredEntities.length}`,
    );

    return context;
  }

  /**
   * Update conversation stage
   */
  async updateConversationStage(
    sessionId: string,
    stage: ConversationContext['conversationStage'],
  ): Promise<ConversationContext> {
    const context = await this.conversationStateService.getConversationContext(sessionId);
    
    if (!context) {
      throw new Error(`Conversation session ${sessionId} not found`);
    }

    const previousStage = context.conversationStage;
    context.conversationStage = stage;
    context.metadata.lastActiveAt = new Date();

    await this.conversationStateService.setConversationContext(sessionId, context);

    this.logger.debug(
      `Updated conversation stage for session ${sessionId}: ${previousStage} -> ${stage}`,
    );

    return context;
  }

  /**
   * Get conversation history (last N messages)
   */
  async getConversationHistory(
    sessionId: string,
    limit: number = 10,
  ): Promise<ChatMessage[]> {
    const context = await this.conversationStateService.getConversationContext(sessionId);
    
    if (!context) {
      return [];
    }

    return context.messages.slice(-limit);
  }

  /**
   * End conversation session
   */
  async endConversation(sessionId: string): Promise<void> {
    await this.conversationStateService.deleteConversationContext(sessionId);
    this.logger.log(`Ended conversation session ${sessionId}`);
  }

  /**
   * Get all active sessions for a user
   */
  async getUserActiveSessions(userId: string): Promise<string[]> {
    return this.conversationStateService.getUserActiveSessions(userId);
  }

  /**
   * Generate a unique session ID
   */
  private generateSessionId(): string {
    return `chat_${uuidv4()}`;
  }

  /**
   * Automatically update conversation stage based on context
   */
  private autoUpdateConversationStage(
    context: ConversationContext,
    intent?: UserIntent,
  ): void {
    const messageCount = context.messages.length;
    const currentStage = context.conversationStage;

    // Stage progression logic
    if (currentStage === 'greeting' && messageCount >= 2) {
      context.conversationStage = 'discovery';
    } else if (currentStage === 'discovery' && intent?.type === 'refinement') {
      context.conversationStage = 'refinement';
    } else if (
      (currentStage === 'discovery' || currentStage === 'refinement') &&
      context.discoveredEntities.length >= 3
    ) {
      context.conversationStage = 'recommendation';
    } else if (intent?.type === 'comparison') {
      context.conversationStage = 'comparison';
    }
  }
}
