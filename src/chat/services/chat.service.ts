import { Injectable, Logger, Inject } from '@nestjs/common';
import { ConversationManagerService } from './conversation-manager.service';
import { ChatErrorHandlerService } from './chat-error-handler.service';
import { LlmFailoverService } from './llm-failover.service';
import { ChatPerformanceMonitorService } from './chat-performance-monitor.service';
import { ChatCacheService } from './chat-cache.service';
import { QuestionSuggestionService } from './question-suggestion.service'; // 🎯 NEW: Smart question generation
import { ResponseVariationService } from './response-variation.service'; // 🎯 NEW: Response variation
import { EntitiesService } from '../../entities/entities.service';
import { ILlmService } from '../../common/llm/interfaces/llm.service.interface';
import { LlmFactoryService } from '../../common/llm/services/llm-factory.service';
import { SendChatMessageDto } from '../dto/send-chat-message.dto';
import { ChatResponseDto } from '../dto/chat-response.dto';
import { ConversationHistoryResponseDto } from '../dto/conversation-history-response.dto';
import { GetConversationHistoryDto } from '../dto/get-conversation-history.dto';

/**
 * Main chat service that orchestrates conversation flow, entity discovery, and LLM interactions
 */
@Injectable()
export class ChatService {
  private readonly logger = new Logger(ChatService.name);

  constructor(
    private readonly conversationManager: ConversationManagerService,
    private readonly chatErrorHandler: ChatErrorHandlerService,
    private readonly llmFailover: LlmFailoverService,
    private readonly performanceMonitor: ChatPerformanceMonitorService,
    private readonly cacheService: ChatCacheService,
    private readonly questionSuggestion: QuestionSuggestionService, // 🎯 NEW: Smart question generation
    private readonly responseVariation: ResponseVariationService, // 🎯 NEW: Response variation
    private readonly entitiesService: EntitiesService,
    @Inject('ILlmService') private readonly llmService: ILlmService,
    private readonly llmFactoryService: LlmFactoryService,
  ) {}

  /**
   * Process a chat message and generate a response
   */
  async sendMessage(
    userId: string,
    sendChatMessageDto: SendChatMessageDto,
  ): Promise<ChatResponseDto> {
    const startTime = Date.now();
    const requestId = this.performanceMonitor.recordRequestStart(
      sendChatMessageDto.session_id || 'new',
    );

    this.logger.log(
      `Processing chat message for user ${userId}, session: ${sendChatMessageDto.session_id || 'new'}, request: ${requestId}`,
    );

    try {
      // Get or create conversation context with error handling
      let context;
      try {
        context = await this.conversationManager.getOrCreateConversation(
          userId,
          sendChatMessageDto.session_id,
        );
      } catch (error) {
        this.logger.error('Failed to get/create conversation context', error.stack);
        return this.chatErrorHandler.handleConversationStateError(
          error,
          sendChatMessageDto.session_id || `chat_${Date.now()}`,
          userId,
        );
      }

      // Update user preferences if provided
      if (sendChatMessageDto.user_preferences) {
        try {
          await this.conversationManager.updateUserPreferences(
            context.sessionId,
            sendChatMessageDto.user_preferences,
          );
        } catch (error) {
          this.logger.warn('Failed to update user preferences', error.stack);
          // Continue without preferences update
        }
      }

      // Add user message to conversation
      let updatedContext;
      try {
        updatedContext = await this.conversationManager.addMessage(
          context.sessionId,
          {
            role: 'user',
            content: sendChatMessageDto.message,
            metadata: sendChatMessageDto.context,
          },
        );
      } catch (error) {
        this.logger.error('Failed to add user message to conversation', error.stack);
        updatedContext = context; // Use original context as fallback
      }

      // Discover relevant entities based on the message with error handling
      let candidateEntities;
      try {
        candidateEntities = await this.discoverRelevantEntities(
          sendChatMessageDto.message,
          updatedContext,
        );
      } catch (error) {
        this.logger.warn('Entity discovery failed, continuing without entities', error.stack);
        candidateEntities = [];
      }

      // Get chat response from LLM with failover
      let chatResponse;
      try {
        chatResponse = await this.llmFailover.getChatResponseWithFailover(
          sendChatMessageDto.message,
          updatedContext,
          candidateEntities,
        );
      } catch (error) {
        this.logger.error('All LLM providers failed', error.stack);
        return this.chatErrorHandler.handleLlmError(
          error,
          updatedContext,
          sendChatMessageDto.message,
        );
      }

      // Add assistant message to conversation with error handling
      let finalContext = updatedContext;
      try {
        finalContext = await this.conversationManager.addMessage(
          context.sessionId,
          {
            role: 'assistant',
            content: chatResponse.message,
            metadata: {
              intent: chatResponse.intent,
              llmProvider: chatResponse.metadata.llmProvider,
              responseTime: chatResponse.metadata.responseTime,
            },
          },
          chatResponse.intent,
        );
      } catch (error) {
        this.logger.warn('Failed to add assistant message to conversation', error.stack);
        // Continue with the response even if we can't save it
      }

      // Update discovered entities if any
      if (chatResponse.discoveredEntities && chatResponse.discoveredEntities.length > 0) {
        try {
          const entityIds = chatResponse.discoveredEntities.map(e => e.id);
          await this.conversationManager.addDiscoveredEntities(context.sessionId, entityIds);
        } catch (error) {
          this.logger.warn('Failed to update discovered entities', error.stack);
          // Continue without updating entities
        }
      }

      // Update conversation stage if changed
      if (chatResponse.conversationStage !== finalContext.conversationStage) {
        try {
          await this.conversationManager.updateConversationStage(
            context.sessionId,
            chatResponse.conversationStage,
          );
        } catch (error) {
          this.logger.warn('Failed to update conversation stage', error.stack);
          // Continue without updating stage
        }
      }

      // 🎯 Apply response variation to prevent repetitive behavior
      let variedResponse = chatResponse;
      try {
        variedResponse = this.responseVariation.addVariationToResponse(
          chatResponse,
          sendChatMessageDto.message,
          updatedContext,
        );

        // Add contextual variation based on user's communication style
        variedResponse = this.responseVariation.addContextualVariation(variedResponse, updatedContext);

        // Ensure response uniqueness
        variedResponse = this.responseVariation.ensureResponseUniqueness(variedResponse, updatedContext);

        this.logger.log(`Applied response variation for session ${context.sessionId}`);
      } catch (error) {
        this.logger.warn('Failed to apply response variation, using original response', error.stack);
        variedResponse = chatResponse;
      }

      // 🎯 Generate smart follow-up questions using our QuestionSuggestionService
      // This replaces the repetitive LLM-generated questions with contextual, non-repetitive ones
      let smartFollowUpQuestions: string[] = [];
      try {
        smartFollowUpQuestions = this.questionSuggestion.generateFollowUpQuestions(
          finalContext,
          sendChatMessageDto.message,
          2, // Max 2 questions to keep it focused
        );
        this.logger.log(`Generated ${smartFollowUpQuestions.length} smart follow-up questions for session ${context.sessionId}`);
      } catch (error) {
        this.logger.warn('Failed to generate smart follow-up questions, using LLM fallback', error.stack);
        smartFollowUpQuestions = variedResponse.followUpQuestions || [];
      }

      // Build response DTO using the varied response
      const response: ChatResponseDto = {
        message: variedResponse.message,
        session_id: context.sessionId,
        conversation_stage: variedResponse.conversationStage,
        suggested_actions: chatResponse.suggestedActions?.map(action => ({
          type: action.type,
          label: action.label,
          data: action.data,
        })),
        discovered_entities: chatResponse.discoveredEntities?.map(entity => ({
          id: entity.id,
          name: entity.name,
          relevance_score: entity.relevanceScore,
          reason: entity.reason,
        })),
        follow_up_questions: smartFollowUpQuestions, // 🎯 Use smart questions instead of LLM-generated ones
        should_transition_to_recommendations: chatResponse.shouldTransitionToRecommendations,
        metadata: {
          response_time: Date.now() - startTime,
          llm_provider: chatResponse.metadata.llmProvider,
          tokens_used: chatResponse.metadata.tokensUsed,
        },
        generated_at: new Date(),
      };

      // Record performance metrics
      this.performanceMonitor.recordRequestComplete(
        requestId,
        response.metadata.response_time,
        true,
        response.metadata.llm_provider,
        response.conversation_stage,
      );

      this.logger.log(
        `Chat response generated for session ${context.sessionId} in ${response.metadata.response_time}ms`,
      );

      return response;
    } catch (error) {
      this.logger.error('Critical error processing chat message', error.stack);

      // Record failed request
      this.performanceMonitor.recordRequestComplete(
        requestId,
        Date.now() - startTime,
        false,
        'FALLBACK',
        'error',
        error.message,
      );

      // Return a graceful fallback response instead of throwing
      return this.chatErrorHandler.createCriticalErrorFallback(
        sendChatMessageDto.session_id || `chat_${Date.now()}`,
        error,
      );
    }
  }

  /**
   * Get conversation history for a session
   */
  async getConversationHistory(
    userId: string,
    sessionId: string,
    getHistoryDto: GetConversationHistoryDto,
  ): Promise<ConversationHistoryResponseDto> {
    this.logger.log(`Getting conversation history for session ${sessionId}`);

    try {
      const context = await this.conversationManager.getOrCreateConversation(userId, sessionId);

      // Verify the session belongs to the user
      if (context.userId !== userId) {
        this.chatErrorHandler.handleAuthError(
          new Error('Session does not belong to the user'),
          sessionId,
        );
      }

      const messages = await this.conversationManager.getConversationHistory(
        sessionId,
        getHistoryDto.limit,
      );

      const response: ConversationHistoryResponseDto = {
        session_id: sessionId,
        conversation_stage: context.conversationStage,
        messages: messages.map(msg => ({
          id: msg.id,
          role: msg.role,
          content: msg.content,
          timestamp: msg.timestamp,
          metadata: msg.metadata,
        })),
        total_messages: context.metadata.totalMessages,
        discovered_entities_count: context.discoveredEntities.length,
        started_at: context.metadata.startedAt,
        last_active_at: context.metadata.lastActiveAt,
      };

      return response;
    } catch (error) {
      this.logger.error('Error getting conversation history', error.stack);
      throw error; // Re-throw for controller to handle
    }
  }

  /**
   * End a conversation session
   */
  async endConversation(userId: string, sessionId: string): Promise<void> {
    this.logger.log(`Ending conversation session ${sessionId} for user ${userId}`);

    // Verify the session belongs to the user
    const context = await this.conversationManager.getOrCreateConversation(userId, sessionId);
    if (context.userId !== userId) {
      throw new Error('Session does not belong to the user');
    }

    await this.conversationManager.endConversation(sessionId);
  }

  /**
   * Get all active sessions for a user
   */
  async getUserActiveSessions(userId: string): Promise<string[]> {
    return this.conversationManager.getUserActiveSessions(userId);
  }

  /**
   * Discover relevant entities based on user message and conversation context
   */
  private async discoverRelevantEntities(
    userMessage: string,
    context: any,
  ): Promise<any[]> {
    try {
      // Use cached entity search for better performance
      const vectorResults = await this.cacheService.getOrSetEntitySearch(
        userMessage,
        async () => {
          return Promise.race([
            this.entitiesService.vectorSearch({
              query: userMessage,
              limit: 10, // Get more candidates for better LLM analysis
            }),
            new Promise<never>((_, reject) =>
              setTimeout(() => reject(new Error('Vector search timeout')), 15000)
            ),
          ]);
        },
      );

      if (vectorResults.length === 0) {
        this.logger.debug('No entities found through vector search');
        return [];
      }

      // Filter based on user preferences if available
      let filteredResults = vectorResults;

      try {
        if (context.userPreferences?.excluded_categories?.length > 0) {
          filteredResults = vectorResults.filter(entity => {
            try {
              const entityCategories = entity.categories?.map((c: any) => c.category.name) || [];
              return !context.userPreferences.excluded_categories.some((excluded: string) =>
                entityCategories.includes(excluded)
              );
            } catch (filterError) {
              this.logger.warn('Error filtering entity by excluded categories', filterError);
              return true; // Include entity if filtering fails
            }
          });
        }

        if (context.userPreferences?.preferred_categories?.length > 0) {
          // Boost entities in preferred categories
          filteredResults = filteredResults.sort((a, b) => {
            try {
              const aCategoriesMatch = a.categories?.some((c: any) =>
                context.userPreferences.preferred_categories.includes(c.category.name)
              ) || false;
              const bCategoriesMatch = b.categories?.some((c: any) =>
                context.userPreferences.preferred_categories.includes(c.category.name)
              ) || false;

              if (aCategoriesMatch && !bCategoriesMatch) return -1;
              if (!aCategoriesMatch && bCategoriesMatch) return 1;
              return 0;
            } catch (sortError) {
              this.logger.warn('Error sorting entities by preferences', sortError);
              return 0; // Keep original order if sorting fails
            }
          });
        }
      } catch (preferencesError) {
        this.logger.warn('Error applying user preferences to entity filtering', preferencesError);
        // Continue with unfiltered results
      }

      this.logger.debug(
        `Found ${filteredResults.length} relevant entities for chat context`,
      );

      return filteredResults.slice(0, 5); // Limit to top 5 for LLM context
    } catch (error) {
      this.logger.error('Error discovering relevant entities', error.stack);

      // Check if this is a critical error that should be handled specially
      if (error.message?.includes('database') || error.message?.includes('connection')) {
        throw error; // Let the caller handle database errors
      }

      return []; // Return empty array for other errors
    }
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics(): any {
    return this.performanceMonitor.getMetrics();
  }

  /**
   * Get performance health status
   */
  getPerformanceHealth(): any {
    return this.performanceMonitor.getHealthStatus();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): any {
    return this.cacheService.getCacheStats();
  }

  /**
   * Clear caches (admin operation)
   */
  clearCaches(): void {
    this.cacheService.clearAllCaches();
  }

  /**
   * Get current LLM provider for response metadata
   */
  private async getCurrentLlmProvider(): Promise<string> {
    try {
      // This is a simple way to get the current provider
      // In a more sophisticated setup, you might want to track this differently
      const providers = this.llmFactoryService.getAvailableProviders();
      return providers[0] || 'UNKNOWN';
    } catch (error) {
      this.logger.error('Error getting current LLM provider', error.stack);
      return 'UNKNOWN';
    }
  }
}
