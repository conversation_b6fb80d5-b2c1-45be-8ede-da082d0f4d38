import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../common/database/prisma.service';
import {
  IConversationStateService,
  ConversationStateConfig,
} from '../interfaces/conversation-state.interface';
import { ConversationContext } from '../../common/llm/interfaces/llm.service.interface';

/**
 * Database-backed implementation of conversation state service
 * Persists conversations across server restarts
 */
@Injectable()
export class DatabaseConversationStateService implements IConversationStateService {
  private readonly logger = new Logger(DatabaseConversationStateService.name);

  constructor(private readonly prisma: PrismaService) {}

  async setConversationContext(
    sessionId: string,
    context: ConversationContext,
    ttlSeconds?: number,
  ): Promise<void> {
    try {
      const now = new Date();
      const expiresAt = new Date(now.getTime() + (ttlSeconds || 3600) * 1000);

      // Store conversation context in database
      await this.prisma.$executeRaw`
        INSERT INTO conversation_sessions (
          session_id, 
          user_id, 
          context_data, 
          created_at, 
          updated_at, 
          expires_at
        ) VALUES (
          ${sessionId},
          ${context.userId},
          ${JSON.stringify(context)}::jsonb,
          ${now},
          ${now},
          ${expiresAt}
        )
        ON CONFLICT (session_id) 
        DO UPDATE SET 
          context_data = ${JSON.stringify(context)}::jsonb,
          updated_at = ${now},
          expires_at = ${expiresAt}
      `;

      this.logger.debug(
        `Stored conversation context for session ${sessionId}, user ${context.userId}, expires at ${expiresAt.toISOString()}`,
      );
    } catch (error) {
      this.logger.error(`Failed to store conversation context for session ${sessionId}`, error);
      throw error;
    }
  }

  async getConversationContext(sessionId: string): Promise<ConversationContext | null> {
    try {
      const result = await this.prisma.$queryRaw<Array<{
        context_data: any;
        expires_at: Date;
      }>>`
        SELECT context_data, expires_at 
        FROM conversation_sessions 
        WHERE session_id = ${sessionId} 
        AND expires_at > NOW()
      `;

      if (!result || result.length === 0) {
        this.logger.debug(`No conversation context found for session ${sessionId}`);
        return null;
      }

      const session = result[0];
      
      // Update last accessed time
      await this.prisma.$executeRaw`
        UPDATE conversation_sessions 
        SET updated_at = NOW() 
        WHERE session_id = ${sessionId}
      `;

      this.logger.debug(`Retrieved conversation context for session ${sessionId}`);
      return session.context_data as ConversationContext;
    } catch (error) {
      this.logger.error(`Failed to retrieve conversation context for session ${sessionId}`, error);
      return null;
    }
  }

  async deleteConversationContext(sessionId: string): Promise<void> {
    try {
      await this.prisma.$executeRaw`
        DELETE FROM conversation_sessions 
        WHERE session_id = ${sessionId}
      `;
      
      this.logger.debug(`Deleted conversation context for session ${sessionId}`);
    } catch (error) {
      this.logger.error(`Failed to delete conversation context for session ${sessionId}`, error);
      throw error;
    }
  }

  async getUserActiveSessions(userId: string): Promise<string[]> {
    try {
      const result = await this.prisma.$queryRaw<Array<{ session_id: string }>>`
        SELECT session_id 
        FROM conversation_sessions 
        WHERE user_id = ${userId} 
        AND expires_at > NOW()
        ORDER BY updated_at DESC
      `;

      return result.map(row => row.session_id);
    } catch (error) {
      this.logger.error(`Failed to get active sessions for user ${userId}`, error);
      return [];
    }
  }

  async cleanupExpiredSessions(): Promise<number> {
    try {
      const result = await this.prisma.$executeRaw`
        DELETE FROM conversation_sessions 
        WHERE expires_at <= NOW()
      `;

      this.logger.debug(`Cleaned up ${result} expired conversation sessions`);
      return result as number;
    } catch (error) {
      this.logger.error('Failed to cleanup expired sessions', error);
      return 0;
    }
  }

  async getSessionStats(): Promise<{
    totalSessions: number;
    activeSessions: number;
    expiredSessions: number;
  }> {
    try {
      const [totalResult, activeResult, expiredResult] = await Promise.all([
        this.prisma.$queryRaw<Array<{ count: bigint }>>`
          SELECT COUNT(*) as count FROM conversation_sessions
        `,
        this.prisma.$queryRaw<Array<{ count: bigint }>>`
          SELECT COUNT(*) as count FROM conversation_sessions WHERE expires_at > NOW()
        `,
        this.prisma.$queryRaw<Array<{ count: bigint }>>`
          SELECT COUNT(*) as count FROM conversation_sessions WHERE expires_at <= NOW()
        `,
      ]);

      return {
        totalSessions: Number(totalResult[0]?.count || 0),
        activeSessions: Number(activeResult[0]?.count || 0),
        expiredSessions: Number(expiredResult[0]?.count || 0),
      };
    } catch (error) {
      this.logger.error('Failed to get session stats', error);
      return { totalSessions: 0, activeSessions: 0, expiredSessions: 0 };
    }
  }
}
