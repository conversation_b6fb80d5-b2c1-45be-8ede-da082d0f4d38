import { Test, TestingModule } from '@nestjs/testing';
import { ChatService } from './services/chat.service';
import { ConversationManagerService } from './services/conversation-manager.service';
import { QuestionSuggestionService } from './services/question-suggestion.service';
import { ResponseVariationService } from './services/response-variation.service';

/**
 * Debug test to identify the exact cause of repetitive behavior
 */
describe('Debug Repetition Issue', () => {
  let chatService: ChatService;
  let conversationManager: ConversationManagerService;
  let responseVariation: ResponseVariationService;

  beforeEach(async () => {
    // Mock all dependencies
    const mockConversationManager = {
      getOrCreateConversation: jest.fn(),
      addMessage: jest.fn(),
    };

    const mockQuestionSuggestion = {
      generateFollowUpQuestions: jest.fn().mockReturnValue([
        'What specific features are you looking for?',
        'What\'s your budget range?'
      ]),
    };

    const mockResponseVariation = {
      addVariationToResponse: jest.fn(),
      addContextualVariation: jest.fn(),
      ensureResponseUniqueness: jest.fn(),
    };

    const mockLlmFailover = {
      getChatResponseWithFailover: jest.fn(),
    };

    const mockEntitiesService = {
      searchEntities: jest.fn().mockResolvedValue([]),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ChatService,
        { provide: ConversationManagerService, useValue: mockConversationManager },
        { provide: QuestionSuggestionService, useValue: mockQuestionSuggestion },
        { provide: ResponseVariationService, useValue: mockResponseVariation },
        { provide: 'LlmFailoverService', useValue: mockLlmFailover },
        { provide: 'EntitiesService', useValue: mockEntitiesService },
        { provide: 'ChatErrorHandlerService', useValue: {} },
        { provide: 'ChatPerformanceMonitorService', useValue: {} },
        { provide: 'ChatCacheService', useValue: {} },
        { provide: 'ILlmService', useValue: {} },
        { provide: 'LlmFactoryService', useValue: {} },
      ],
    }).compile();

    chatService = module.get<ChatService>(ChatService);
    conversationManager = module.get<ConversationManagerService>(ConversationManagerService);
    responseVariation = module.get<ResponseVariationService>(ResponseVariationService);
  });

  it('should demonstrate the repetition issue with same question', async () => {
    const userId = 'test-user';
    const sessionId = 'test-session';
    const question = "What's the best AI tool for content creation?";

    // Mock conversation context with previous identical question
    const mockContext = {
      sessionId,
      userId,
      messages: [
        {
          id: '1',
          role: 'user' as const,
          content: question,
          timestamp: new Date(Date.now() - 60000), // 1 minute ago
        },
        {
          id: '2', 
          role: 'assistant' as const,
          content: 'I\'d love to help you find the right AI tools! Could you tell me more about what you\'re trying to achieve?',
          timestamp: new Date(Date.now() - 59000),
        }
      ],
      discoveredEntities: [],
      userPreferences: {},
      conversationStage: 'discovery' as const,
      metadata: {
        startedAt: new Date(Date.now() - 120000),
        lastActiveAt: new Date(),
        totalMessages: 2,
        entitiesShown: [],
      },
    };

    // Mock the conversation manager to return this context
    (conversationManager.getOrCreateConversation as jest.Mock).mockResolvedValue(mockContext);
    (conversationManager.addMessage as jest.Mock).mockResolvedValue({
      ...mockContext,
      messages: [
        ...mockContext.messages,
        {
          id: '3',
          role: 'user' as const,
          content: question, // Same question again!
          timestamp: new Date(),
        }
      ]
    });

    console.log('🧪 Testing repetition scenario:');
    console.log('Previous conversation:', mockContext.messages.map(m => `${m.role}: ${m.content}`));
    console.log('User asks same question again:', question);

    // This should demonstrate how the LLM sees the conversation
    // and whether our anti-repetition logic works
  });

  it('should show conversation history being passed to LLM', () => {
    const mockContext = {
      sessionId: 'test',
      userId: 'test',
      messages: [
        { id: '1', role: 'user' as const, content: 'What\'s the best AI tool for content creation?', timestamp: new Date() },
        { id: '2', role: 'assistant' as const, content: 'I can help you find great content creation tools!', timestamp: new Date() },
        { id: '3', role: 'user' as const, content: 'What\'s the best AI tool for content creation?', timestamp: new Date() },
      ],
      discoveredEntities: [],
      userPreferences: {},
      conversationStage: 'discovery' as const,
      metadata: { startedAt: new Date(), lastActiveAt: new Date(), totalMessages: 3, entitiesShown: [] },
    };

    // Simulate what the LLM sees
    const conversationHistory = mockContext.messages
      .slice(-10)
      .map(msg => `${msg.role}: ${msg.content}`)
      .join('\n');

    console.log('🔍 Conversation history passed to LLM:');
    console.log(conversationHistory);
    console.log('\n📝 Analysis:');
    console.log('- User asked the same question twice');
    console.log('- LLM can see this repetition');
    console.log('- Our anti-repetition rules should handle this');

    // Check if we can detect the repetition
    const userMessages = mockContext.messages.filter(m => m.role === 'user');
    const hasDuplicateQuestions = userMessages.some((msg, index) => 
      userMessages.slice(index + 1).some(otherMsg => 
        msg.content.toLowerCase() === otherMsg.content.toLowerCase()
      )
    );

    expect(hasDuplicateQuestions).toBe(true);
    console.log('✅ Duplicate question detected:', hasDuplicateQuestions);
  });

  it('should test our topic extraction logic', () => {
    const messages = [
      { role: 'user', content: 'What\'s the best AI tool for content creation?' },
      { role: 'assistant', content: 'I can help you find great content creation tools!' },
      { role: 'user', content: 'What\'s the best AI tool for content creation?' },
    ];

    // Test our topic extraction
    const topics = new Set<string>();
    const topicKeywords = {
      'content_creation': ['content', 'creation', 'creative'],
      'ai_tools': ['ai', 'tool', 'tools'],
      'best_recommendations': ['best', 'top', 'recommend'],
    };

    messages.forEach(msg => {
      const content = msg.content.toLowerCase();
      Object.entries(topicKeywords).forEach(([topic, keywords]) => {
        if (keywords.some(keyword => content.includes(keyword))) {
          topics.add(topic);
        }
      });
    });

    console.log('🏷️ Extracted topics:', Array.from(topics));
    expect(topics.has('content_creation')).toBe(true);
    expect(topics.has('ai_tools')).toBe(true);
  });

  it('should test ResponseVariationService with real implementation', () => {
    // Create a real instance for testing
    const realResponseVariation = new ResponseVariationService();

    const mockContext = {
      sessionId: 'test',
      userId: 'test',
      messages: [
        { id: '1', role: 'user' as const, content: 'What\'s the best AI tool for content creation?', timestamp: new Date() },
        { id: '2', role: 'assistant' as const, content: 'I can help you find great content creation tools!', timestamp: new Date() },
        { id: '3', role: 'user' as const, content: 'What\'s the best AI tool for content creation?', timestamp: new Date() },
      ],
      discoveredEntities: [],
      userPreferences: {},
      conversationStage: 'discovery' as const,
      metadata: { startedAt: new Date(), lastActiveAt: new Date(), totalMessages: 3, entitiesShown: [] },
    };

    const originalResponse = {
      message: 'I can help you find great content creation tools!',
      conversationStage: 'discovery' as const,
      followUpQuestions: ['What type of content?', 'What\'s your budget?'],
    };

    const variedResponse = realResponseVariation.addVariationToResponse(
      originalResponse,
      'What\'s the best AI tool for content creation?',
      mockContext
    );

    console.log('🧪 Testing ResponseVariationService:');
    console.log('Original message:', originalResponse.message);
    console.log('Varied message:', variedResponse.message);
    console.log('Original questions:', originalResponse.followUpQuestions);
    console.log('Varied questions:', variedResponse.followUpQuestions);

    // The varied response should be different
    expect(variedResponse.message).not.toBe(originalResponse.message);
    expect(variedResponse.message).toContain('again'); // Should acknowledge repetition

    console.log('✅ Response variation working correctly!');
  });
});
