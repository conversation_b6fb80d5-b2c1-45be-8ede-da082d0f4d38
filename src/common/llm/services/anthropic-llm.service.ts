import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  ILlmService,
  LlmRecommendation,
  CandidateEntity,
  ChatResponse,
  ConversationContext,
  UserIntent,
} from '../interfaces/llm.service.interface';

@Injectable()
export class AnthropicLlmService implements ILlmService {
  private readonly logger = new Logger(AnthropicLlmService.name);
  private readonly apiKey: string | undefined;
  private readonly apiUrl = 'https://api.anthropic.com/v1/messages';

  constructor(private readonly configService: ConfigService) {
    this.apiKey = this.configService.get<string>('ANTHROPIC_API_KEY');
    if (!this.apiKey) {
      this.logger.warn('ANTHROPIC_API_KEY is not set in environment variables.');
    }
  }

  async getRecommendation(
    problemDescription: string,
    candidateEntities: CandidateEntity[],
  ): Promise<LlmRecommendation> {
    this.logger.log(
      `Getting Anthropic recommendation for problem: "${problemDescription}" with ${candidateEntities.length} candidates`,
    );

    if (!this.apiKey) {
      this.logger.warn('Anthropic API key not available, using fallback');
      return this.getFallbackRecommendation(candidateEntities);
    }

    try {
      const prompt = this.buildRecommendationPrompt(
        problemDescription,
        candidateEntities,
      );

      const response = await this.callAnthropicAPI(prompt);
      const recommendation = this.parseAnthropicResponse(response, candidateEntities);

      this.logger.log(
        `Anthropic recommendation generated: ${recommendation.recommendedEntityIds.length} entities recommended`,
      );

      return recommendation;
    } catch (error) {
      this.logger.error('Error generating Anthropic recommendation', error.stack);
      return this.getFallbackRecommendation(candidateEntities);
    }
  }

  private async callAnthropicAPI(prompt: string): Promise<string> {
    const requestBody = {
      model: 'claude-3-haiku-20240307',
      max_tokens: 1000,
      temperature: 0.7,
      messages: [
        {
          role: 'user',
          content: prompt,
        },
      ],
    };

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'anthropic-version': '2023-06-01',
    };

    if (this.apiKey) {
      headers['x-api-key'] = this.apiKey;
    }

    const response = await fetch(this.apiUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      throw new Error(`Anthropic API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.content && data.content[0]?.text) {
      return data.content[0].text;
    }

    throw new Error('Invalid response format from Anthropic API');
  }

  private buildRecommendationPrompt(
    problemDescription: string,
    candidateEntities: CandidateEntity[],
  ): string {
    const entitiesContext = candidateEntities
      .map((entity, index) => {
        const categories = entity.categories
          .map((c) => c.category.name)
          .join(', ');
        const tags = entity.tags.map((t) => t.tag.name).join(', ');
        const features = entity.features.map((f) => f.feature.name).join(', ');

        return `${index + 1}. **${entity.name}** (ID: ${entity.id})
   - Type: ${entity.entityType.name}
   - Description: ${entity.shortDescription || entity.description || 'No description available'}
   - Categories: ${categories || 'None'}
   - Tags: ${tags || 'None'}
   - Features: ${features || 'None'}
   - Rating: ${entity.avgRating ? `${entity.avgRating}/5 (${entity.reviewCount} reviews)` : 'No ratings'}`;
      })
      .join('\n\n');

    return `You are an AI assistant helping users find the best AI tools and resources for their specific needs.

**User's Problem:**
"${problemDescription}"

**Available Options:**
${entitiesContext}

**Instructions:**
1. Analyze the user's problem and requirements
2. Recommend the TOP 3-5 most relevant options from the list above
3. Provide a clear explanation of why each recommendation fits the user's needs
4. Consider factors like: relevance to the problem, tool capabilities, user ratings, ease of use, and cost

**Response Format:**
Please respond in the following JSON format:
{
  "recommendedEntityIds": ["entity-id-1", "entity-id-2", "entity-id-3"],
  "explanation": "Based on your need for [problem summary], I recommend: 1) [Tool Name] because [specific reason]... 2) [Tool Name] because [specific reason]... 3) [Tool Name] because [specific reason]..."
}

**Important:** Only include entity IDs that exist in the provided list above. Limit to maximum 5 recommendations.`;
  }

  private parseAnthropicResponse(
    response: string,
    candidateEntities: CandidateEntity[],
  ): LlmRecommendation {
    try {
      // Try to extract JSON from the response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const parsed = JSON.parse(jsonMatch[0]);
      
      // Validate the response structure
      if (!parsed.recommendedEntityIds || !Array.isArray(parsed.recommendedEntityIds)) {
        throw new Error('Invalid response structure');
      }

      // Filter to only include valid entity IDs
      const validEntityIds = candidateEntities.map((e) => e.id);
      const filteredIds = parsed.recommendedEntityIds.filter((id: string) =>
        validEntityIds.includes(id),
      );

      return {
        recommendedEntityIds: filteredIds.slice(0, 5), // Limit to 5
        explanation: parsed.explanation || 'AI recommendation generated successfully.',
      };
    } catch (error) {
      this.logger.warn('Failed to parse Anthropic response, using fallback', error.message);
      return this.getFallbackRecommendation(candidateEntities);
    }
  }

  private getFallbackRecommendation(candidateEntities: CandidateEntity[]): LlmRecommendation {
    // Simple fallback: return top 3 entities by rating, or first 3 if no ratings
    const sortedEntities = candidateEntities
      .sort((a, b) => (b.avgRating || 0) - (a.avgRating || 0))
      .slice(0, 3);

    return {
      recommendedEntityIds: sortedEntities.map((e) => e.id),
      explanation:
        'Based on the available options, here are the top-rated tools that might help with your needs. Please review each option to see which best fits your specific requirements.',
    };
  }

  // Chat-specific methods implementation (following same patterns as OpenAI/Gemini)
  async getChatResponse(
    userMessage: string,
    context: ConversationContext,
    candidateEntities?: CandidateEntity[],
  ): Promise<ChatResponse> {
    const startTime = Date.now();

    this.logger.log(
      `Getting Anthropic chat response for session: ${context.sessionId}, stage: ${context.conversationStage}`,
    );

    if (!this.apiKey) {
      this.logger.warn('Anthropic API key not available, using fallback');
      return this.getFallbackChatResponse(userMessage, context);
    }

    try {
      const intent = await this.classifyIntent(userMessage, context);
      const prompt = this.buildChatPrompt(userMessage, context, intent, candidateEntities);
      const response = await this.callAnthropicAPI(prompt);
      const chatResponse = this.parseChatResponse(response, intent, context, candidateEntities);

      chatResponse.metadata = {
        responseTime: Date.now() - startTime,
        llmProvider: 'ANTHROPIC',
      };

      return chatResponse;
    } catch (error) {
      this.logger.error('Error generating Anthropic chat response', error.stack);
      return this.getFallbackChatResponse(userMessage, context);
    }
  }

  async classifyIntent(userMessage: string, context: ConversationContext): Promise<UserIntent> {
    if (!this.apiKey) return this.getFallbackIntent();

    try {
      const prompt = this.buildIntentClassificationPrompt(userMessage, context);
      const response = await this.callAnthropicAPI(prompt);
      return this.parseIntentResponse(response);
    } catch (error) {
      this.logger.error('Error classifying intent', error.stack);
      return this.getFallbackIntent();
    }
  }

  async generateFollowUpQuestions(context: ConversationContext): Promise<string[]> {
    if (!this.apiKey) return this.getFallbackFollowUpQuestions(context);

    try {
      const prompt = this.buildFollowUpPrompt(context);
      const response = await this.callAnthropicAPI(prompt);
      return this.parseFollowUpResponse(response);
    } catch (error) {
      this.logger.error('Error generating follow-up questions', error.stack);
      return this.getFallbackFollowUpQuestions(context);
    }
  }

  async shouldTransitionToRecommendations(context: ConversationContext): Promise<{ shouldTransition: boolean; reason: string }> {
    if (!this.apiKey) return { shouldTransition: false, reason: 'API key not available' };

    try {
      const prompt = this.buildTransitionPrompt(context);
      const response = await this.callAnthropicAPI(prompt);
      return this.parseTransitionResponse(response);
    } catch (error) {
      this.logger.error('Error evaluating transition', error.stack);
      return { shouldTransition: false, reason: 'Error in evaluation' };
    }
  }

  // Helper methods (reusing patterns from other providers)
  private buildChatPrompt(userMessage: string, context: ConversationContext, intent: UserIntent, candidateEntities?: CandidateEntity[]): string {
    // Use more conversation history for better context awareness
    const conversationHistory = context.messages.slice(-10).map(msg => `${msg.role}: ${msg.content}`).join('\n');
    const entitiesContext = candidateEntities ? this.formatEntitiesForChat(candidateEntities) : '';
    const userProfile = this.formatUserProfile(context);

    // Extract topics and questions already discussed
    const discussedTopics = this.extractDiscussedTopics(context);
    const previousQuestions = this.extractPreviousQuestions(context);

    return `You are an expert AI assistant helping users discover the perfect AI tools for their needs. You are conversational, helpful, and knowledgeable about AI tools and their applications.

**CRITICAL: ANTI-REPETITION RULES**
- NEVER ask questions that have already been asked in this conversation
- NEVER repeat the same topics or suggestions you've already covered
- BUILD upon previous knowledge rather than starting over
- If the user has already provided information, acknowledge it and move forward
- Vary your language and approach even when covering similar ground

**Current Conversation Context:**
- Stage: ${context.conversationStage}
- User Intent: ${intent.type} (confidence: ${intent.confidence})
- Session: ${context.sessionId}
- Messages in conversation: ${context.messages?.length || 0}

**User Profile:**
${userProfile}

**Full Conversation History:**
${conversationHistory}

**Topics Already Discussed:**
${discussedTopics.length > 0 ? discussedTopics.join(', ') : 'None yet'}

**Questions Already Asked:**
${previousQuestions.length > 0 ? previousQuestions.join('\n- ') : 'None yet'}

**Current User Message:**
"${userMessage}"

${entitiesContext ? `**Relevant AI Tools to Consider:**\n${entitiesContext}` : ''}

**Instructions:**
1. Respond naturally and conversationally to the user's message
2. Based on the intent (${intent.type}), guide the conversation appropriately
3. If entities are provided, mention relevant ones naturally in your response
4. ONLY ask NEW questions that haven't been covered before
5. Build upon information already gathered rather than re-asking
6. Be encouraging and helpful throughout the conversation
7. If you've already asked about their work/industry/needs, don't ask again
8. Progress the conversation forward based on what you already know

**Response Format (JSON):**
{
  "message": "Your conversational response here",
  "discoveredEntities": [{"id": "entity-id", "name": "Tool Name", "relevanceScore": 0.9, "reason": "Why it's relevant"}],
  "followUpQuestions": ["Only NEW questions that haven't been asked before"],
  "suggestedActions": [{"type": "ask_question", "label": "Tell me more about...", "data": {}}],
  "shouldTransitionToRecommendations": false,
  "conversationStage": "${context.conversationStage}"
}

Keep your message natural, helpful, and engaging. Focus on understanding their needs and guiding them toward the right AI tools WITHOUT repeating yourself.`;
  }

  private buildIntentClassificationPrompt(userMessage: string, context: ConversationContext): string {
    const recentMessages = context.messages.slice(-3).map(msg => `${msg.role}: ${msg.content}`).join('\n');

    return `Analyze the user's intent from their message and conversation context.

**Conversation Context:**
${recentMessages}

**Current User Message:**
"${userMessage}"

**Intent Types:**
- discovery: User is exploring what AI tools exist for their needs
- comparison: User wants to compare specific tools or categories
- specific_tool: User is asking about a particular tool
- general_question: User has general questions about AI tools
- refinement: User is narrowing down their requirements

**Response Format (JSON):**
{
  "type": "discovery|comparison|specific_tool|general_question|refinement",
  "confidence": 0.85,
  "entities": ["mentioned entity names"],
  "categories": ["mentioned categories"],
  "features": ["mentioned features"],
  "constraints": {
    "budget": "free|low|medium|high",
    "technical_level": "beginner|intermediate|advanced",
    "use_case": "specific use case if mentioned"
  }
}`;
  }

  private buildFollowUpPrompt(context: ConversationContext): string {
    const lastMessage = context.messages[context.messages.length - 1];

    return `Generate 2-3 intelligent follow-up questions to help the user discover the right AI tools.

**Conversation Stage:** ${context.conversationStage}
**User's Last Message:** "${lastMessage?.content || 'No previous message'}"
**Discovered Entities:** ${context.discoveredEntities.length} tools found so far

**Guidelines:**
- Ask questions that help narrow down their specific needs
- Consider their technical level and use case
- Be conversational and helpful
- Focus on practical aspects like budget, features, or use cases

**Response Format (JSON):**
{
  "questions": ["Question 1?", "Question 2?", "Question 3?"]
}`;
  }

  private buildTransitionPrompt(context: ConversationContext): string {
    return `Determine if the conversation is ready to transition to formal recommendations.

**Conversation Stage:** ${context.conversationStage}
**Messages Count:** ${context.messages.length}
**Discovered Entities:** ${context.discoveredEntities.length}
**User Preferences:** ${JSON.stringify(context.userPreferences)}

**Criteria for Transition:**
- User has clearly expressed their needs
- We have enough information about their requirements
- User seems ready for specific recommendations
- Conversation has progressed beyond initial discovery

**Response Format (JSON):**
{
  "shouldTransition": true/false,
  "reason": "Explanation of the decision"
}`;
  }

  // Parsing and helper methods (following same patterns as other providers)
  private parseChatResponse(response: string, intent: UserIntent, context: ConversationContext, candidateEntities?: CandidateEntity[]): ChatResponse {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) throw new Error('No JSON found in response');

      const parsed = JSON.parse(jsonMatch[0]);

      return {
        message: parsed.message || 'I\'m here to help you find the perfect AI tools!',
        intent,
        discoveredEntities: parsed.discoveredEntities || [],
        followUpQuestions: parsed.followUpQuestions || [],
        suggestedActions: parsed.suggestedActions || [],
        shouldTransitionToRecommendations: parsed.shouldTransitionToRecommendations || false,
        conversationStage: parsed.conversationStage || context.conversationStage,
        metadata: { responseTime: 0, llmProvider: 'ANTHROPIC' },
      };
    } catch (error) {
      this.logger.warn('Failed to parse Anthropic chat response, using fallback', error.message);
      return this.getFallbackChatResponse('', context);
    }
  }

  private parseIntentResponse(response: string): UserIntent {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) throw new Error('No JSON found in response');

      const parsed = JSON.parse(jsonMatch[0]);

      return {
        type: parsed.type || 'discovery',
        confidence: parsed.confidence || 0.5,
        entities: parsed.entities || [],
        categories: parsed.categories || [],
        features: parsed.features || [],
        constraints: parsed.constraints || {},
      };
    } catch (error) {
      this.logger.warn('Failed to parse intent response, using fallback', error.message);
      return this.getFallbackIntent();
    }
  }

  private parseFollowUpResponse(response: string): string[] {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) throw new Error('No JSON found in response');

      const parsed = JSON.parse(jsonMatch[0]);
      return parsed.questions || [];
    } catch (error) {
      this.logger.warn('Failed to parse follow-up response, using fallback', error.message);
      return [];
    }
  }

  private parseTransitionResponse(response: string): { shouldTransition: boolean; reason: string } {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) throw new Error('No JSON found in response');

      const parsed = JSON.parse(jsonMatch[0]);
      return {
        shouldTransition: parsed.shouldTransition || false,
        reason: parsed.reason || 'Automatic evaluation',
      };
    } catch (error) {
      this.logger.warn('Failed to parse transition response, using fallback', error.message);
      return { shouldTransition: false, reason: 'Error in evaluation' };
    }
  }

  private formatEntitiesForChat(entities: CandidateEntity[]): string {
    return entities.slice(0, 5).map((entity, index) => {
      const categories = entity.categories.map(c => c.category.name).join(', ');
      const features = entity.features.map(f => f.feature.name).join(', ');

      return `${index + 1}. **${entity.name}**
   - ${entity.shortDescription || entity.description || 'AI tool'}
   - Categories: ${categories || 'General'}
   - Key Features: ${features || 'Various features'}
   - Rating: ${entity.avgRating ? `${entity.avgRating}/5` : 'Not rated'}`;
    }).join('\n\n');
  }

  private formatUserProfile(context: ConversationContext): string {
    const prefs = context.userPreferences;
    return `- Technical Level: ${prefs.technical_level || 'Not specified'}
- Budget Preference: ${prefs.budget || 'Not specified'}
- Preferred Categories: ${prefs.preferred_categories?.join(', ') || 'None specified'}
- Conversation Stage: ${context.conversationStage}
- Tools Discovered: ${context.discoveredEntities.length}`;
  }

  /**
   * Extract topics that have been discussed in the conversation
   */
  private extractDiscussedTopics(context: ConversationContext): string[] {
    const topics = new Set<string>();
    const messages = context.messages || [];

    // Common topic keywords to look for
    const topicKeywords = {
      'work/industry': ['work', 'job', 'industry', 'business', 'company', 'profession', 'career'],
      'technical level': ['beginner', 'intermediate', 'advanced', 'expert', 'technical', 'experience'],
      'budget': ['budget', 'cost', 'price', 'expensive', 'cheap', 'free', 'paid'],
      'programming': ['code', 'coding', 'programming', 'development', 'developer', 'software'],
      'education': ['education', 'teaching', 'learning', 'student', 'school', 'university'],
      'content creation': ['content', 'writing', 'video', 'image', 'design', 'creative'],
      'automation': ['automation', 'automate', 'workflow', 'process', 'efficiency'],
      'data analysis': ['data', 'analysis', 'analytics', 'insights', 'reporting'],
    };

    messages.forEach(msg => {
      const content = msg.content.toLowerCase();
      Object.entries(topicKeywords).forEach(([topic, keywords]) => {
        if (keywords.some(keyword => content.includes(keyword))) {
          topics.add(topic);
        }
      });
    });

    return Array.from(topics);
  }

  /**
   * Extract questions that have been asked by the assistant
   */
  private extractPreviousQuestions(context: ConversationContext): string[] {
    const questions: string[] = [];
    const messages = context.messages || [];

    messages.forEach(msg => {
      if (msg.role === 'assistant' && msg.content.includes('?')) {
        // Extract questions from assistant messages
        const questionMatches = msg.content.match(/[^.!]*\?/g);
        if (questionMatches) {
          questions.push(...questionMatches.map(q => q.trim()));
        }
      }
    });

    return questions;
  }

  private getFallbackChatResponse(userMessage: string, context: ConversationContext): ChatResponse {
    const fallbackMessages = {
      greeting: "Hello! I'm here to help you discover the perfect AI tools for your needs. What kind of tasks are you looking to accomplish?",
      discovery: "I'd love to help you find the right AI tools! Could you tell me more about what you're trying to achieve?",
      refinement: "Let me help you narrow down the options. What specific features or capabilities are most important to you?",
      recommendation: "Based on our conversation, I can help you find some great options. Would you like me to show you some specific recommendations?",
      comparison: "I can help you compare different AI tools. What specific aspects would you like me to focus on?"
    };

    return {
      message: fallbackMessages[context.conversationStage] || fallbackMessages.discovery,
      intent: this.getFallbackIntent(),
      followUpQuestions: this.getFallbackFollowUpQuestions(context),
      shouldTransitionToRecommendations: false,
      conversationStage: context.conversationStage,
      metadata: { responseTime: 0, llmProvider: 'ANTHROPIC_FALLBACK' },
    };
  }

  private getFallbackIntent(): UserIntent {
    return {
      type: 'discovery',
      confidence: 0.5,
      entities: [],
      categories: [],
      features: [],
      constraints: {},
    };
  }

  private getFallbackFollowUpQuestions(context: ConversationContext): string[] {
    const questionsByStage = {
      greeting: ["What type of work or projects are you working on?", "Are you looking for tools for business or personal use?"],
      discovery: ["What's your experience level with AI tools?", "Do you have a budget in mind for AI tools?", "What's the main challenge you're trying to solve?"],
      refinement: ["Would you prefer free tools or are you open to paid options?", "How technical do you want the tool to be?", "Do you need integration with other software?"],
      recommendation: ["Would you like to see some specific recommendations?", "Should I focus on the most popular options or newer tools?"],
      comparison: ["What criteria are most important for your decision?", "Would you like me to compare pricing or features?"]
    };

    return questionsByStage[context.conversationStage] || questionsByStage.discovery;
  }
}
