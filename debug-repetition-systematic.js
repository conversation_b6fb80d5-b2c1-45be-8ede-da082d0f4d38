const axios = require('axios');

/**
 * Systematic debugging script for chat repetition issue
 * Tests each potential cause one by one
 */
class ChatRepetitionDebugger {
  constructor(baseURL = 'http://localhost:3000', authToken = 'your-jwt-token-here') {
    this.baseURL = baseURL;
    this.authToken = authToken;
    this.headers = {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json'
    };
  }

  async debugStep1_ConversationMemory() {
    console.log('\n🔍 DEBUG STEP 1: Testing Conversation Memory');
    console.log('='.repeat(50));

    const sessionId = 'chat_342f3df6-26d4-4e65-8744-93f373d3d577';
    
    try {
      // Clear the session first
      console.log('1. Clearing session...');
      await axios.get(`${this.baseURL}/debug/chat/clear/${sessionId}`, { headers: this.headers });
      
      // Check initial state
      console.log('2. Checking initial session state...');
      const initialState = await axios.get(`${this.baseURL}/debug/chat/session/${sessionId}`, { headers: this.headers });
      console.log('Initial state:', initialState.data);

      // Send first message
      console.log('3. Sending first message...');
      const response1 = await axios.post(`${this.baseURL}/chat`, {
        message: "What's the best AI tool for content creation?",
        session_id: sessionId
      }, { headers: this.headers });
      
      console.log('First response preview:', response1.data.message.substring(0, 100) + '...');

      // Check conversation state after first message
      console.log('4. Checking conversation state after first message...');
      const stateAfterFirst = await axios.get(`${this.baseURL}/debug/chat/session/${sessionId}`, { headers: this.headers });
      console.log('State after first message:', {
        messageCount: stateAfterFirst.data.messageCount,
        messages: stateAfterFirst.data.messages
      });

      // Send second message (same question)
      console.log('5. Sending same message again...');
      const response2 = await axios.post(`${this.baseURL}/chat`, {
        message: "What's the best AI tool for content creation?",
        session_id: sessionId
      }, { headers: this.headers });
      
      console.log('Second response preview:', response2.data.message.substring(0, 100) + '...');

      // Check final conversation state
      console.log('6. Checking final conversation state...');
      const finalState = await axios.get(`${this.baseURL}/debug/chat/session/${sessionId}`, { headers: this.headers });
      console.log('Final state:', {
        messageCount: finalState.data.messageCount,
        messages: finalState.data.messages
      });

      // Analysis
      console.log('\n📊 ANALYSIS:');
      console.log('Responses identical:', response1.data.message === response2.data.message ? '❌ YES' : '✅ NO');
      console.log('Expected message count: 4 (2 user + 2 assistant)');
      console.log('Actual message count:', finalState.data.messageCount);
      console.log('Assistant responses saved:', finalState.data.messages.filter(m => m.role === 'assistant').length);

      return {
        responsesIdentical: response1.data.message === response2.data.message,
        expectedMessageCount: 4,
        actualMessageCount: finalState.data.messageCount,
        assistantResponsesSaved: finalState.data.messages.filter(m => m.role === 'assistant').length
      };

    } catch (error) {
      console.error('❌ Debug Step 1 failed:', error.response?.data || error.message);
      return { error: error.message };
    }
  }

  async debugStep2_SessionRetrieval() {
    console.log('\n🔍 DEBUG STEP 2: Testing Session Retrieval');
    console.log('='.repeat(50));

    const sessionId = 'chat_test_session_retrieval';
    
    try {
      // Send a message to create session
      console.log('1. Creating session with first message...');
      const response1 = await axios.post(`${this.baseURL}/chat`, {
        message: "Hello, this is a test message",
        session_id: sessionId
      }, { headers: this.headers });

      // Check if session exists
      console.log('2. Checking if session was created...');
      const sessionCheck1 = await axios.get(`${this.baseURL}/debug/chat/session/${sessionId}`, { headers: this.headers });
      console.log('Session exists after creation:', sessionCheck1.data.exists);

      // Send another message with same session ID
      console.log('3. Sending second message with same session ID...');
      const response2 = await axios.post(`${this.baseURL}/chat`, {
        message: "This is the second message",
        session_id: sessionId
      }, { headers: this.headers });

      // Verify session was retrieved (not recreated)
      console.log('4. Verifying session was retrieved...');
      const sessionCheck2 = await axios.get(`${this.baseURL}/debug/chat/session/${sessionId}`, { headers: this.headers });
      
      console.log('📊 RESULTS:');
      console.log('Session ID consistent:', response1.data.session_id === response2.data.session_id ? '✅ YES' : '❌ NO');
      console.log('Message count progression:', sessionCheck2.data.messageCount >= 2 ? '✅ YES' : '❌ NO');

      return {
        sessionIdConsistent: response1.data.session_id === response2.data.session_id,
        messageCountProgression: sessionCheck2.data.messageCount >= 2
      };

    } catch (error) {
      console.error('❌ Debug Step 2 failed:', error.response?.data || error.message);
      return { error: error.message };
    }
  }

  async debugStep3_LLMContextPassing() {
    console.log('\n🔍 DEBUG STEP 3: Testing LLM Context Passing');
    console.log('='.repeat(50));

    const sessionId = 'chat_test_llm_context';
    
    try {
      // Clear session
      await axios.get(`${this.baseURL}/debug/chat/clear/${sessionId}`, { headers: this.headers });

      // Send a message with specific context
      console.log('1. Sending message with specific context...');
      const response1 = await axios.post(`${this.baseURL}/chat`, {
        message: "My name is John and I work in marketing",
        session_id: sessionId
      }, { headers: this.headers });

      // Ask about the context
      console.log('2. Asking about previous context...');
      const response2 = await axios.post(`${this.baseURL}/chat`, {
        message: "What did I tell you about my name and job?",
        session_id: sessionId
      }, { headers: this.headers });

      console.log('Context response:', response2.data.message);

      // Check if LLM remembers the context
      const remembersName = response2.data.message.toLowerCase().includes('john');
      const remembersJob = response2.data.message.toLowerCase().includes('marketing');

      console.log('📊 RESULTS:');
      console.log('Remembers name (John):', remembersName ? '✅ YES' : '❌ NO');
      console.log('Remembers job (marketing):', remembersJob ? '✅ YES' : '❌ NO');

      return {
        remembersName,
        remembersJob,
        contextWorking: remembersName && remembersJob
      };

    } catch (error) {
      console.error('❌ Debug Step 3 failed:', error.response?.data || error.message);
      return { error: error.message };
    }
  }

  async debugStep4_CacheIssues() {
    console.log('\n🔍 DEBUG STEP 4: Testing Cache Issues');
    console.log('='.repeat(50));

    try {
      // Get all sessions to check cache state
      console.log('1. Checking cache state...');
      const allSessions = await axios.get(`${this.baseURL}/debug/chat/sessions`, { headers: this.headers });
      console.log('Total cached sessions:', allSessions.data.totalSessions);
      console.log('Sample sessions:', allSessions.data.sessions.slice(0, 3));

      // Test with completely new session ID
      const uniqueSessionId = `chat_unique_${Date.now()}`;
      console.log(`2. Testing with unique session ID: ${uniqueSessionId}`);

      const response1 = await axios.post(`${this.baseURL}/chat`, {
        message: "What's the best AI tool for content creation?",
        session_id: uniqueSessionId
      }, { headers: this.headers });

      const response2 = await axios.post(`${this.baseURL}/chat`, {
        message: "What's the best AI tool for content creation?",
        session_id: uniqueSessionId
      }, { headers: this.headers });

      console.log('📊 RESULTS:');
      console.log('Responses identical with unique session:', response1.data.message === response2.data.message ? '❌ YES' : '✅ NO');

      return {
        totalCachedSessions: allSessions.data.totalSessions,
        responsesIdenticalWithUniqueSession: response1.data.message === response2.data.message
      };

    } catch (error) {
      console.error('❌ Debug Step 4 failed:', error.response?.data || error.message);
      return { error: error.message };
    }
  }

  async runAllDebugSteps() {
    console.log('🚀 STARTING SYSTEMATIC DEBUG OF CHAT REPETITION ISSUE');
    console.log('='.repeat(60));

    const results = {};

    results.step1 = await this.debugStep1_ConversationMemory();
    results.step2 = await this.debugStep2_SessionRetrieval();
    results.step3 = await this.debugStep3_LLMContextPassing();
    results.step4 = await this.debugStep4_CacheIssues();

    console.log('\n🎯 FINAL DIAGNOSIS');
    console.log('='.repeat(60));

    // Analyze results
    if (results.step1.error) {
      console.log('❌ Cannot complete diagnosis - authentication or server issues');
      return;
    }

    if (results.step1.actualMessageCount < results.step1.expectedMessageCount) {
      console.log('🎯 ROOT CAUSE: Assistant responses are not being saved to conversation');
      console.log('   - Expected messages: 4, Actual:', results.step1.actualMessageCount);
      console.log('   - Assistant responses saved:', results.step1.assistantResponsesSaved);
    } else if (!results.step2.sessionIdConsistent) {
      console.log('🎯 ROOT CAUSE: Session management issues - sessions not being reused');
    } else if (!results.step3.contextWorking) {
      console.log('🎯 ROOT CAUSE: LLM not receiving conversation context properly');
    } else if (results.step4.responsesIdenticalWithUniqueSession) {
      console.log('🎯 ROOT CAUSE: LLM provider or caching issues');
    } else {
      console.log('🤔 UNCLEAR: All tests passed but repetition still occurs');
      console.log('   - Check server logs for additional clues');
      console.log('   - Verify LLM provider configuration');
    }

    return results;
  }
}

// Usage
if (require.main === module) {
  const debugger = new ChatRepetitionDebugger();
  
  console.log('⚠️  IMPORTANT: Update the authToken in this script before running!');
  console.log('You can get a JWT token by logging in through the API or frontend.\n');
  
  debugger.runAllDebugSteps().catch(console.error);
}

module.exports = ChatRepetitionDebugger;
