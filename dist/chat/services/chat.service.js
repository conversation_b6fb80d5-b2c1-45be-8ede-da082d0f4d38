"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ChatService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatService = void 0;
const common_1 = require("@nestjs/common");
const conversation_manager_service_1 = require("./conversation-manager.service");
const chat_error_handler_service_1 = require("./chat-error-handler.service");
const llm_failover_service_1 = require("./llm-failover.service");
const chat_performance_monitor_service_1 = require("./chat-performance-monitor.service");
const chat_cache_service_1 = require("./chat-cache.service");
const question_suggestion_service_1 = require("./question-suggestion.service");
const response_variation_service_1 = require("./response-variation.service");
const entities_service_1 = require("../../entities/entities.service");
const llm_factory_service_1 = require("../../common/llm/services/llm-factory.service");
let ChatService = ChatService_1 = class ChatService {
    constructor(conversationManager, chatErrorHandler, llmFailover, performanceMonitor, cacheService, questionSuggestion, responseVariation, entitiesService, llmService, llmFactoryService) {
        this.conversationManager = conversationManager;
        this.chatErrorHandler = chatErrorHandler;
        this.llmFailover = llmFailover;
        this.performanceMonitor = performanceMonitor;
        this.cacheService = cacheService;
        this.questionSuggestion = questionSuggestion;
        this.responseVariation = responseVariation;
        this.entitiesService = entitiesService;
        this.llmService = llmService;
        this.llmFactoryService = llmFactoryService;
        this.logger = new common_1.Logger(ChatService_1.name);
    }
    async sendMessage(userId, sendChatMessageDto) {
        const startTime = Date.now();
        const requestId = this.performanceMonitor.recordRequestStart(sendChatMessageDto.session_id || 'new');
        this.logger.log(`Processing chat message for user ${userId}, session: ${sendChatMessageDto.session_id || 'new'}, request: ${requestId}`);
        try {
            let context;
            try {
                context = await this.conversationManager.getOrCreateConversation(userId, sendChatMessageDto.session_id);
            }
            catch (error) {
                this.logger.error('Failed to get/create conversation context', error.stack);
                return this.chatErrorHandler.handleConversationStateError(error, sendChatMessageDto.session_id || `chat_${Date.now()}`, userId);
            }
            if (sendChatMessageDto.user_preferences) {
                try {
                    await this.conversationManager.updateUserPreferences(context.sessionId, sendChatMessageDto.user_preferences);
                }
                catch (error) {
                    this.logger.warn('Failed to update user preferences', error.stack);
                }
            }
            let updatedContext;
            try {
                updatedContext = await this.conversationManager.addMessage(context.sessionId, {
                    role: 'user',
                    content: sendChatMessageDto.message,
                    metadata: sendChatMessageDto.context,
                });
            }
            catch (error) {
                this.logger.error('Failed to add user message to conversation', error.stack);
                updatedContext = context;
            }
            let candidateEntities;
            try {
                candidateEntities = await this.discoverRelevantEntities(sendChatMessageDto.message, updatedContext);
            }
            catch (error) {
                this.logger.warn('Entity discovery failed, continuing without entities', error.stack);
                candidateEntities = [];
            }
            let chatResponse;
            try {
                chatResponse = await this.llmFailover.getChatResponseWithFailover(sendChatMessageDto.message, updatedContext, candidateEntities);
            }
            catch (error) {
                this.logger.error('All LLM providers failed', error.stack);
                return this.chatErrorHandler.handleLlmError(error, updatedContext, sendChatMessageDto.message);
            }
            let finalContext = updatedContext;
            try {
                finalContext = await this.conversationManager.addMessage(context.sessionId, {
                    role: 'assistant',
                    content: chatResponse.message,
                    metadata: {
                        intent: chatResponse.intent,
                        llmProvider: chatResponse.metadata.llmProvider,
                        responseTime: chatResponse.metadata.responseTime,
                    },
                }, chatResponse.intent);
            }
            catch (error) {
                this.logger.warn('Failed to add assistant message to conversation', error.stack);
            }
            if (chatResponse.discoveredEntities && chatResponse.discoveredEntities.length > 0) {
                try {
                    const entityIds = chatResponse.discoveredEntities.map(e => e.id);
                    await this.conversationManager.addDiscoveredEntities(context.sessionId, entityIds);
                }
                catch (error) {
                    this.logger.warn('Failed to update discovered entities', error.stack);
                }
            }
            if (chatResponse.conversationStage !== finalContext.conversationStage) {
                try {
                    await this.conversationManager.updateConversationStage(context.sessionId, chatResponse.conversationStage);
                }
                catch (error) {
                    this.logger.warn('Failed to update conversation stage', error.stack);
                }
            }
            let variedResponse = chatResponse;
            this.logger.log(`Using original response without variation for session ${context.sessionId}`);
            let smartFollowUpQuestions = [];
            try {
                smartFollowUpQuestions = this.questionSuggestion.generateFollowUpQuestions(finalContext, sendChatMessageDto.message, 2);
                this.logger.log(`Generated ${smartFollowUpQuestions.length} smart follow-up questions for session ${context.sessionId}`);
            }
            catch (error) {
                this.logger.warn('Failed to generate smart follow-up questions, using LLM fallback', error.stack);
                smartFollowUpQuestions = variedResponse.followUpQuestions || [];
            }
            try {
                finalContext = await this.conversationManager.addMessage(context.sessionId, {
                    role: 'assistant',
                    content: variedResponse.message,
                    metadata: {
                        llmProvider: variedResponse.metadata?.llmProvider,
                        tokensUsed: variedResponse.metadata?.tokensUsed,
                        conversationStage: variedResponse.conversationStage,
                    },
                });
                this.logger.log(`Saved assistant response to conversation ${context.sessionId}`);
            }
            catch (error) {
                this.logger.error('Failed to save assistant response to conversation', error.stack);
                finalContext = updatedContext;
            }
            const response = {
                message: variedResponse.message,
                session_id: context.sessionId,
                conversation_stage: variedResponse.conversationStage,
                suggested_actions: chatResponse.suggestedActions?.map(action => ({
                    type: action.type,
                    label: action.label,
                    data: action.data,
                })),
                discovered_entities: chatResponse.discoveredEntities?.map(entity => ({
                    id: entity.id,
                    name: entity.name,
                    relevance_score: entity.relevanceScore,
                    reason: entity.reason,
                })),
                follow_up_questions: smartFollowUpQuestions,
                should_transition_to_recommendations: chatResponse.shouldTransitionToRecommendations,
                metadata: {
                    response_time: Date.now() - startTime,
                    llm_provider: chatResponse.metadata.llmProvider,
                    tokens_used: chatResponse.metadata.tokensUsed,
                },
                generated_at: new Date(),
            };
            this.performanceMonitor.recordRequestComplete(requestId, response.metadata.response_time, true, response.metadata.llm_provider, response.conversation_stage);
            this.logger.log(`Chat response generated for session ${context.sessionId} in ${response.metadata.response_time}ms`);
            return response;
        }
        catch (error) {
            this.logger.error('Critical error processing chat message', error.stack);
            this.performanceMonitor.recordRequestComplete(requestId, Date.now() - startTime, false, 'FALLBACK', 'error', error.message);
            return this.chatErrorHandler.createCriticalErrorFallback(sendChatMessageDto.session_id || `chat_${Date.now()}`, error);
        }
    }
    async getConversationHistory(userId, sessionId, getHistoryDto) {
        this.logger.log(`Getting conversation history for session ${sessionId}`);
        try {
            const context = await this.conversationManager.getOrCreateConversation(userId, sessionId);
            if (context.userId !== userId) {
                this.chatErrorHandler.handleAuthError(new Error('Session does not belong to the user'), sessionId);
            }
            const messages = await this.conversationManager.getConversationHistory(sessionId, getHistoryDto.limit);
            const response = {
                session_id: sessionId,
                conversation_stage: context.conversationStage,
                messages: messages.map(msg => ({
                    id: msg.id,
                    role: msg.role,
                    content: msg.content,
                    timestamp: msg.timestamp,
                    metadata: msg.metadata,
                })),
                total_messages: context.metadata.totalMessages,
                discovered_entities_count: context.discoveredEntities.length,
                started_at: context.metadata.startedAt,
                last_active_at: context.metadata.lastActiveAt,
            };
            return response;
        }
        catch (error) {
            this.logger.error('Error getting conversation history', error.stack);
            throw error;
        }
    }
    async endConversation(userId, sessionId) {
        this.logger.log(`Ending conversation session ${sessionId} for user ${userId}`);
        const context = await this.conversationManager.getOrCreateConversation(userId, sessionId);
        if (context.userId !== userId) {
            throw new Error('Session does not belong to the user');
        }
        await this.conversationManager.endConversation(sessionId);
    }
    async getUserActiveSessions(userId) {
        return this.conversationManager.getUserActiveSessions(userId);
    }
    async discoverRelevantEntities(userMessage, context) {
        try {
            const vectorResults = await this.cacheService.getOrSetEntitySearch(userMessage, async () => {
                return Promise.race([
                    this.entitiesService.vectorSearch({
                        query: userMessage,
                        limit: 10,
                    }),
                    new Promise((_, reject) => setTimeout(() => reject(new Error('Vector search timeout')), 15000)),
                ]);
            });
            if (vectorResults.length === 0) {
                this.logger.debug('No entities found through vector search');
                return [];
            }
            let filteredResults = vectorResults;
            try {
                if (context.userPreferences?.excluded_categories?.length > 0) {
                    filteredResults = vectorResults.filter(entity => {
                        try {
                            const entityCategories = entity.categories?.map((c) => c.category.name) || [];
                            return !context.userPreferences.excluded_categories.some((excluded) => entityCategories.includes(excluded));
                        }
                        catch (filterError) {
                            this.logger.warn('Error filtering entity by excluded categories', filterError);
                            return true;
                        }
                    });
                }
                if (context.userPreferences?.preferred_categories?.length > 0) {
                    filteredResults = filteredResults.sort((a, b) => {
                        try {
                            const aCategoriesMatch = a.categories?.some((c) => context.userPreferences.preferred_categories.includes(c.category.name)) || false;
                            const bCategoriesMatch = b.categories?.some((c) => context.userPreferences.preferred_categories.includes(c.category.name)) || false;
                            if (aCategoriesMatch && !bCategoriesMatch)
                                return -1;
                            if (!aCategoriesMatch && bCategoriesMatch)
                                return 1;
                            return 0;
                        }
                        catch (sortError) {
                            this.logger.warn('Error sorting entities by preferences', sortError);
                            return 0;
                        }
                    });
                }
            }
            catch (preferencesError) {
                this.logger.warn('Error applying user preferences to entity filtering', preferencesError);
            }
            this.logger.debug(`Found ${filteredResults.length} relevant entities for chat context`);
            return filteredResults.slice(0, 5);
        }
        catch (error) {
            this.logger.error('Error discovering relevant entities', error.stack);
            if (error.message?.includes('database') || error.message?.includes('connection')) {
                throw error;
            }
            return [];
        }
    }
    getPerformanceMetrics() {
        return this.performanceMonitor.getMetrics();
    }
    getPerformanceHealth() {
        return this.performanceMonitor.getHealthStatus();
    }
    getCacheStats() {
        return this.cacheService.getCacheStats();
    }
    clearCaches() {
        this.cacheService.clearAllCaches();
    }
    async getCurrentLlmProvider() {
        try {
            const providers = this.llmFactoryService.getAvailableProviders();
            return providers[0] || 'UNKNOWN';
        }
        catch (error) {
            this.logger.error('Error getting current LLM provider', error.stack);
            return 'UNKNOWN';
        }
    }
};
exports.ChatService = ChatService;
exports.ChatService = ChatService = ChatService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(8, (0, common_1.Inject)('ILlmService')),
    __metadata("design:paramtypes", [conversation_manager_service_1.ConversationManagerService,
        chat_error_handler_service_1.ChatErrorHandlerService,
        llm_failover_service_1.LlmFailoverService,
        chat_performance_monitor_service_1.ChatPerformanceMonitorService,
        chat_cache_service_1.ChatCacheService,
        question_suggestion_service_1.QuestionSuggestionService,
        response_variation_service_1.ResponseVariationService,
        entities_service_1.EntitiesService, Object, llm_factory_service_1.LlmFactoryService])
], ChatService);
//# sourceMappingURL=chat.service.js.map