import { ConfigService } from '@nestjs/config';
import { ChatResponse, UserIntent } from '../../common/llm/interfaces/llm.service.interface';
export declare class ChatCacheService {
    private readonly configService;
    private readonly logger;
    private readonly intentCache;
    private readonly followUpCache;
    private readonly entitySearchCache;
    private readonly responseCache;
    private readonly config;
    constructor(configService: ConfigService);
    getOrSetIntent(key: string, computeFn: () => Promise<UserIntent>): Promise<UserIntent>;
    getOrSetFollowUpQuestions(key: string, computeFn: () => Promise<string[]>): Promise<string[]>;
    getOrSetEntitySearch(query: string, computeFn: () => Promise<any[]>): Promise<any[]>;
    getOrSetChatResponse(key: string, computeFn: () => Promise<ChatResponse>): Promise<ChatResponse>;
    generateIntentKey(message: string, contextHash: string): string;
    generateFollowUpKey(conversationStage: string, entitiesCount: number, preferencesHash: string): string;
    generateResponseKey(message: string, contextHash: string, entitiesHash: string): string;
    generateContextHash(context: any): string;
    generateEntitiesHash(entities: any[]): string;
    getCacheStats(): any;
    clearAllCaches(): void;
    clearCache(cacheType: 'intent' | 'followUp' | 'entitySearch' | 'response'): void;
    warmUpCaches(commonQueries: string[]): Promise<void>;
    private hashString;
    private normalizeSearchQuery;
    private calculateHitRate;
    private extractDiscussedTopicsFromContext;
}
