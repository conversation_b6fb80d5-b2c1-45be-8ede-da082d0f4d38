"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const memory_conversation_state_service_1 = require("./services/memory-conversation-state.service");
const conversation_manager_service_1 = require("./services/conversation-manager.service");
const chat_service_1 = require("./services/chat.service");
const chat_error_handler_service_1 = require("./services/chat-error-handler.service");
const llm_failover_service_1 = require("./services/llm-failover.service");
const question_suggestion_service_1 = require("./services/question-suggestion.service");
const response_variation_service_1 = require("./services/response-variation.service");
const chat_performance_monitor_service_1 = require("./services/chat-performance-monitor.service");
const chat_cache_service_1 = require("./services/chat-cache.service");
const enhanced_intent_classification_service_1 = require("./services/enhanced-intent-classification.service");
const conversation_flow_manager_service_1 = require("./services/conversation-flow-manager.service");
const intelligent_response_generator_service_1 = require("./services/intelligent-response-generator.service");
const advanced_entity_ranking_service_1 = require("../common/ranking/advanced-entity-ranking.service");
const performance_optimization_service_1 = require("../common/performance/performance-optimization.service");
const chat_controller_1 = require("./chat.controller");
const entities_module_1 = require("../entities/entities.module");
const llm_module_1 = require("../common/llm/llm.module");
const recommendations_module_1 = require("../recommendations/recommendations.module");
let ChatModule = class ChatModule {
};
exports.ChatModule = ChatModule;
exports.ChatModule = ChatModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule,
            entities_module_1.EntitiesModule,
            llm_module_1.LlmModule,
            recommendations_module_1.RecommendationsModule,
        ],
        controllers: [chat_controller_1.ChatController],
        providers: [
            {
                provide: 'IConversationStateService',
                useClass: memory_conversation_state_service_1.MemoryConversationStateService,
            },
            conversation_manager_service_1.ConversationManagerService,
            chat_error_handler_service_1.ChatErrorHandlerService,
            llm_failover_service_1.LlmFailoverService,
            question_suggestion_service_1.QuestionSuggestionService,
            response_variation_service_1.ResponseVariationService,
            chat_performance_monitor_service_1.ChatPerformanceMonitorService,
            chat_cache_service_1.ChatCacheService,
            enhanced_intent_classification_service_1.EnhancedIntentClassificationService,
            conversation_flow_manager_service_1.ConversationFlowManagerService,
            intelligent_response_generator_service_1.IntelligentResponseGeneratorService,
            advanced_entity_ranking_service_1.AdvancedEntityRankingService,
            performance_optimization_service_1.PerformanceOptimizationService,
            chat_service_1.ChatService,
        ],
        exports: [
            'IConversationStateService',
            conversation_manager_service_1.ConversationManagerService,
            chat_service_1.ChatService,
        ],
    })
], ChatModule);
//# sourceMappingURL=chat.module.js.map