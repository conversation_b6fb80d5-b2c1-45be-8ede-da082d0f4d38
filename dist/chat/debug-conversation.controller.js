"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var DebugConversationController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DebugConversationController = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const conversation_manager_service_1 = require("./services/conversation-manager.service");
const memory_conversation_state_service_1 = require("./services/memory-conversation-state.service");
let DebugConversationController = DebugConversationController_1 = class DebugConversationController {
    constructor(conversationManager, conversationState) {
        this.conversationManager = conversationManager;
        this.conversationState = conversationState;
        this.logger = new common_1.Logger(DebugConversationController_1.name);
    }
    async getConversationContext(sessionId) {
        try {
            this.logger.log(`Debug: Getting conversation context for session ${sessionId}`);
            const context = await this.conversationState.getConversationContext(sessionId);
            if (!context) {
                return {
                    error: 'Session not found',
                    sessionId,
                    exists: false
                };
            }
            return {
                sessionId,
                exists: true,
                messageCount: context.messages?.length || 0,
                messages: context.messages?.map((msg, index) => ({
                    index,
                    id: msg.id,
                    role: msg.role,
                    content: msg.content.substring(0, 100) + (msg.content.length > 100 ? '...' : ''),
                    timestamp: msg.timestamp,
                })) || [],
                conversationStage: context.conversationStage,
                totalMessages: context.metadata?.totalMessages,
                lastActiveAt: context.metadata?.lastActiveAt,
                startedAt: context.metadata?.startedAt,
            };
        }
        catch (error) {
            this.logger.error(`Debug: Error getting conversation context for ${sessionId}`, error);
            return {
                error: error.message,
                sessionId,
                exists: false
            };
        }
    }
    async getAllSessions() {
        try {
            const memoryService = this.conversationState;
            const cache = memoryService.cache;
            const sessions = [];
            for (const [sessionId, session] of cache.entries()) {
                sessions.push({
                    sessionId,
                    userId: session.userId,
                    messageCount: session.context.messages?.length || 0,
                    lastActiveAt: session.lastAccessedAt,
                    conversationStage: session.context.conversationStage,
                });
            }
            return {
                totalSessions: sessions.length,
                sessions: sessions.slice(0, 10),
            };
        }
        catch (error) {
            this.logger.error('Debug: Error getting all sessions', error);
            return {
                error: error.message,
                totalSessions: 0,
                sessions: []
            };
        }
    }
    async clearSession(sessionId) {
        try {
            this.logger.log(`Debug: Clearing session ${sessionId}`);
            const memoryService = this.conversationState;
            const cache = memoryService.cache;
            const existed = cache.has(sessionId);
            cache.delete(sessionId);
            return {
                sessionId,
                existed,
                cleared: true,
                message: existed ? 'Session cleared' : 'Session did not exist'
            };
        }
        catch (error) {
            this.logger.error(`Debug: Error clearing session ${sessionId}`, error);
            return {
                error: error.message,
                sessionId,
                cleared: false
            };
        }
    }
};
exports.DebugConversationController = DebugConversationController;
__decorate([
    (0, common_1.Get)('session/:sessionId'),
    __param(0, (0, common_1.Param)('sessionId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DebugConversationController.prototype, "getConversationContext", null);
__decorate([
    (0, common_1.Get)('sessions'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DebugConversationController.prototype, "getAllSessions", null);
__decorate([
    (0, common_1.Get)('clear/:sessionId'),
    __param(0, (0, common_1.Param)('sessionId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DebugConversationController.prototype, "clearSession", null);
exports.DebugConversationController = DebugConversationController = DebugConversationController_1 = __decorate([
    (0, common_1.Controller)('debug/chat'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [conversation_manager_service_1.ConversationManagerService,
        memory_conversation_state_service_1.MemoryConversationStateService])
], DebugConversationController);
//# sourceMappingURL=debug-conversation.controller.js.map