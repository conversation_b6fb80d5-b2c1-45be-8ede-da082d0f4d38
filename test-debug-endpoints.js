const axios = require('axios');

/**
 * Simple test to verify debug endpoints are working
 * Run this after starting the server to test the debug functionality
 */
async function testDebugEndpoints() {
  const baseURL = 'http://localhost:3000';
  
  // Test without authentication first to see if endpoints exist
  console.log('🧪 Testing Debug Endpoints...\n');

  try {
    // Test 1: Check if debug endpoint exists (should get 401 but not 404)
    console.log('1. Testing debug endpoint existence...');
    try {
      await axios.get(`${baseURL}/debug/chat/sessions`);
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Debug endpoint exists (got 401 Unauthorized as expected)');
      } else if (error.response?.status === 404) {
        console.log('❌ Debug endpoint not found (404) - check if debug controller is properly registered');
        return;
      } else {
        console.log(`⚠️ Unexpected response: ${error.response?.status} - ${error.response?.statusText}`);
      }
    }

    // Test 2: Check main chat endpoint
    console.log('2. Testing main chat endpoint...');
    try {
      await axios.post(`${baseURL}/chat`, {
        message: "test"
      });
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Chat endpoint exists (got 401 Unauthorized as expected)');
      } else if (error.response?.status === 404) {
        console.log('❌ Chat endpoint not found (404) - server may not be running');
        return;
      } else {
        console.log(`⚠️ Unexpected response: ${error.response?.status} - ${error.response?.statusText}`);
      }
    }

    console.log('\n📋 NEXT STEPS:');
    console.log('1. Get a valid JWT token by logging in');
    console.log('2. Use the token in Postman to test the debug endpoints');
    console.log('3. Follow the POSTMAN-DEBUG-STEPS.md guide');
    console.log('\n🔍 Debug endpoints available:');
    console.log('- GET /debug/chat/session/{sessionId}');
    console.log('- GET /debug/chat/sessions');
    console.log('- GET /debug/chat/clear/{sessionId}');

  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ Cannot connect to server - make sure it\'s running on http://localhost:3000');
      console.log('Start with: npm run start:dev');
    } else {
      console.log('❌ Unexpected error:', error.message);
    }
  }
}

// Test server logs functionality
function testServerLogs() {
  console.log('\n📝 SERVER LOGS TO WATCH FOR:');
  console.log('When you send a chat message, look for these logs:');
  console.log('');
  console.log('✅ GOOD LOGS:');
  console.log('  🔍 DEBUG: Retrieved conversation context for session {sessionId}');
  console.log('  🔍 DEBUG: Current message count: {number}');
  console.log('  🔍 DEBUG: Last 3 messages: [...]');
  console.log('  ✅ Saved assistant response to conversation {sessionId}');
  console.log('  🔍 DEBUG: Attempted to save assistant response - final context message count: {number}');
  console.log('');
  console.log('❌ BAD LOGS:');
  console.log('  Failed to save assistant response to conversation');
  console.log('  No conversation context found for session');
  console.log('  Failed to get/create conversation context');
  console.log('');
  console.log('🎯 KEY INDICATORS:');
  console.log('  - Message count should increase by 2 after each exchange (user + assistant)');
  console.log('  - "Last 3 messages" should show conversation history');
  console.log('  - No "Failed to save" errors should appear');
}

// Run tests
testDebugEndpoints().then(() => {
  testServerLogs();
});
